import 'package:flutter_test/flutter_test.dart';
import 'package:money_mouthy_two/services/stripe_service.dart';

void main() {
  group('StripeService Tests', () {
    test('should validate minimum amount', () async {
      expect(
        () async => await StripeService.processPayment(0.25),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Minimum amount is \$0.50'),
        )),
      );
    });

    test('should validate maximum amount', () async {
      expect(
        () async => await StripeService.processPayment(1000000.0),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Maximum amount is \$999,999.00'),
        )),
      );
    });

    test('should accept valid amount', () {
      // This test would require Firebase setup, so we just verify the method exists
      expect(StripeService.processPayment, isA<Function>());
    });
  });
}
