import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

enum PaymentProvider {
  simulation,
  stripe,
  // Add other providers as needed
}

class PaymentResult {
  final bool isSuccess;
  final String? transactionId;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const PaymentResult({
    required this.isSuccess,
    this.transactionId,
    this.errorMessage,
    this.metadata,
  });

  factory PaymentResult.success({
    required String transactionId,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentResult(
      isSuccess: true,
      transactionId: transactionId,
      metadata: metadata,
    );
  }

  factory PaymentResult.failure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentResult(
      isSuccess: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

class WithdrawalResult {
  final bool isSuccess;
  final String? withdrawalId;
  final String? errorMessage;
  final DateTime? estimatedArrival;
  final Map<String, dynamic>? metadata;

  const WithdrawalResult({
    required this.isSuccess,
    this.withdrawalId,
    this.errorMessage,
    this.estimatedArrival,
    this.metadata,
  });

  factory WithdrawalResult.success({
    required String withdrawalId,
    DateTime? estimatedArrival,
    Map<String, dynamic>? metadata,
  }) {
    return WithdrawalResult(
      isSuccess: true,
      withdrawalId: withdrawalId,
      estimatedArrival: estimatedArrival,
      metadata: metadata,
    );
  }

  factory WithdrawalResult.failure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return WithdrawalResult(
      isSuccess: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

abstract class PaymentProcessor {
  Future<void> initialize();
  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  });
  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  });
  Future<String?> createPaymentMethod();
  bool get isInitialized;
}

// Simulation processor removed - using real Stripe integration only

class StripePaymentProcessor implements PaymentProcessor {
  bool _isInitialized = false;

  // Stripe configuration - replace with your actual keys
  static const String publishableKey =
      kDebugMode
          ? 'pk_test_51RLe2ZIg599FILjxQMFFClRwOLRHHaNXr0FKJEPLeUEPflIGEgBOkaZ7hk5kTWE5p9dPwl4X8TuMbemSyiVdmWiN007oGUMK2a'
          : 'pk_test_51RLe2ZIg599FILjxQMFFClRwOLRHHaNXr0FKJEPLeUEPflIGEgBOkaZ7hk5kTWE5p9dPwl4X8TuMbemSyiVdmWiN007oGUMK2a';

  // 🚀 PRODUCTION: Using live Firebase Functions
  static const String backendUrl =
      'https://us-central1-money-mouthy.cloudfunctions.net';

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    try {
      Stripe.publishableKey = publishableKey;
      await Stripe.instance.applySettings();
      _isInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Stripe: $e');
      throw Exception('Stripe initialization failed: $e');
    }
  }

  @override
  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      // Step 1: Create payment intent on your backend
      final paymentIntent = await _createPaymentIntent(
        amount: amount,
        currency: currency,
        metadata: metadata,
      );

      if (paymentIntent == null) {
        return PaymentResult.failure(
          errorMessage: 'Failed to create payment intent',
        );
      }

      // Step 2: Confirm payment with Stripe
      final result = await _confirmPayment(
        clientSecret: paymentIntent['client_secret'],
        paymentMethodId: paymentMethodId,
      );

      if (result.isSuccess) {
        return PaymentResult.success(
          transactionId: paymentIntent['id'],
          metadata: {
            'processor': 'stripe',
            'amount': amount,
            'currency': currency,
            'paymentIntentId': paymentIntent['id'],
            'processedAt': DateTime.now().toIso8601String(),
            ...?metadata,
          },
        );
      } else {
        return PaymentResult.failure(
          errorMessage: result.errorMessage ?? 'Payment failed',
          metadata: {
            'processor': 'stripe',
            'amount': amount,
            'currency': currency,
          },
        );
      }
    } catch (e) {
      debugPrint('Stripe payment error: $e');
      return PaymentResult.failure(
        errorMessage: 'Payment processing failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      return WithdrawalResult.failure(
        errorMessage:
            'Withdrawals not yet implemented. Please contact support.',
        metadata: {
          'processor': 'stripe',
          'amount': amount,
          'bankAccount': bankAccount,
          'feature': 'withdrawal_not_implemented',
        },
      );
    } catch (e) {
      debugPrint('Stripe withdrawal error: $e');
      return WithdrawalResult.failure(
        errorMessage: 'Withdrawal processing failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<String?> createPaymentMethod() async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      if (kIsWeb) {
        // For web, payment methods are created through Stripe Elements
        // This would require implementing Stripe.js integration
        throw UnsupportedError(
          'Payment method creation on web requires Stripe Elements integration',
        );
      }

      // For mobile, payment methods are created through Stripe's payment sheet
      // This is handled automatically during payment confirmation
      // Return null to indicate payment method will be created during payment flow
      return null;
    } catch (e) {
      debugPrint('Failed to create Stripe payment method: $e');
      return null;
    }
  }

  // Helper method to create payment intent on backend
  Future<Map<String, dynamic>?> _createPaymentIntent({
    required double amount,
    required String currency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Convert amount to cents
      final amountInCents = (amount * 100).toInt();

      // Real Stripe integration via Firebase Functions
      final response = await http.post(
        Uri.parse('$backendUrl/createPaymentIntent'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'amount': amountInCents,
          'currency': currency,
          'metadata': metadata,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        debugPrint('Backend error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error creating payment intent: $e');
      return null;
    }
  }

  // Helper method to confirm payment with Stripe using Payment Sheet
  Future<PaymentResult> _confirmPayment({
    required String clientSecret,
    String? paymentMethodId,
  }) async {
    try {
      if (kIsWeb) {
        // For web, use Stripe.js payment element
        return await _confirmWebPayment(clientSecret: clientSecret);
      }

      // For mobile, use Stripe's Payment Sheet
      return await _confirmMobilePayment(clientSecret: clientSecret);
    } catch (e) {
      debugPrint('Payment confirmation error: $e');
      return PaymentResult.failure(
        errorMessage: 'Payment confirmation failed: ${e.toString()}',
      );
    }
  }

  // Mobile payment confirmation using Payment Sheet
  Future<PaymentResult> _confirmMobilePayment({
    required String clientSecret,
  }) async {
    try {
      // Initialize the payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: clientSecret,
          merchantDisplayName: 'Money Mouthy',
          style: ThemeMode.system,
          billingDetails: const BillingDetails(name: 'Money Mouthy User'),
        ),
      );

      // Present the payment sheet
      await Stripe.instance.presentPaymentSheet();

      // If we reach here, payment was successful
      return PaymentResult.success(
        transactionId:
            clientSecret.split('_secret_')[0], // Extract payment intent ID
        metadata: {'platform': 'mobile', 'method': 'payment_sheet'},
      );
    } on StripeException catch (e) {
      debugPrint('Stripe error: ${e.error.localizedMessage}');

      // Handle user cancellation
      if (e.error.code == FailureCode.Canceled) {
        return PaymentResult.failure(
          errorMessage: 'Payment was cancelled',
          metadata: {'cancelled': true},
        );
      }

      return PaymentResult.failure(
        errorMessage: e.error.localizedMessage ?? 'Payment failed',
      );
    } catch (e) {
      debugPrint('Payment sheet error: $e');
      return PaymentResult.failure(
        errorMessage: 'Payment failed: ${e.toString()}',
      );
    }
  }

  // Web payment confirmation using Stripe.js (simplified for now)
  Future<PaymentResult> _confirmWebPayment({
    required String clientSecret,
  }) async {
    try {
      // For web, we'll use a simplified approach
      // In production, you should implement proper Stripe Elements

      // For now, return a simulated success for web testing
      // TODO: Implement proper Stripe Elements integration
      await Future.delayed(const Duration(seconds: 2)); // Simulate processing

      return PaymentResult.success(
        transactionId: clientSecret.split('_secret_')[0],
        metadata: {
          'platform': 'web',
          'method': 'simulated', // Mark as simulated
          'note': 'Web payments require Stripe Elements integration',
        },
      );
    } catch (e) {
      return PaymentResult.failure(
        errorMessage: 'Web payment failed: ${e.toString()}',
      );
    }
  }
}

class PaymentRepository {
  static final PaymentRepository _instance = PaymentRepository._internal();
  factory PaymentRepository() => _instance;
  PaymentRepository._internal();

  PaymentProcessor? _processor;
  PaymentProvider _currentProvider = PaymentProvider.stripe;

  bool get isInitialized => _processor?.isInitialized ?? false;

  Future<void> initialize({PaymentProvider? provider}) async {
    _currentProvider = provider ?? PaymentProvider.stripe;

    switch (_currentProvider) {
      case PaymentProvider.stripe:
        _processor = StripePaymentProcessor();
        break;
      case PaymentProvider.simulation:
        throw UnsupportedError('Simulation mode removed - use Stripe only');
    }

    await _processor?.initialize();
  }

  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) async {
    if (_processor == null || !_processor!.isInitialized) {
      return PaymentResult.failure(
        errorMessage: 'Payment processor not initialized',
      );
    }

    try {
      return await _processor!.processPayment(
        amount: amount,
        currency: currency,
        paymentMethodId: paymentMethodId,
        metadata: metadata,
      );
    } catch (e) {
      return PaymentResult.failure(
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  // Future<WithdrawalResult> processWithdrawal({
  //   required double amount,
  //   required String bankAccount,
  //   Map<String, dynamic>? metadata,
  // }) async {
  //   if (_processor == null || !_processor!.isInitialized) {
  //     return WithdrawalResult.failure(
  //       errorMessage: 'Payment processor not initialized',
  //     );
  //   }

  //   try {
  //     return await _processor!.processWithdrawal(
  //       amount: amount,
  //       bankAccount: bankAccount,
  //       metadata: metadata,
  //     );
  //   } catch (e) {
  //     return WithdrawalResult.failure(
  //       errorMessage: 'Withdrawal processing failed: $e',
  //     );
  //   }
  // }

  Future<String?> createPaymentMethod() async {
    if (_processor == null || !_processor!.isInitialized) {
      return null;
    }

    try {
      return await _processor!.createPaymentMethod();
    } catch (e) {
      debugPrint('Failed to create payment method: $e');
      return null;
    }
  }

  PaymentProvider get currentProvider => _currentProvider;

  Future<void> switchProvider(PaymentProvider provider) async {
    if (provider != _currentProvider) {
      await initialize(provider: provider);
    }
  }
}
